// import { useMemo } from 'react';

// import Heading from '~/core/ui/Heading';

// import {
//   ArrowSmallDownIcon,
//   ArrowSmallUpIcon,
//   Bars2Icon,
// } from '@heroicons/react/24/outline';

// const Tile: React.FC<{ children: React.ReactNode }> & {
//   Header: typeof TileHeader;
//   Heading: typeof TileHeading;
//   Body: typeof TileBody;
//   Figure: typeof TileFigure;
//   Trend: typeof TileTrend;
//   Badge: typeof TileBadge;
// } = ({ children }: { children: React.ReactNode }) => {
//   return (
//     //added height h-24 for fix height
//     <div
//       className={
//         'flex flex-col rounded-lg border border-gray-100 bg-background shadow-md h-28' +
//         ' dark:border-dark-900 bg-background p-2'
//       }
//     >
//       <span className='min-w-0'>
//       {children}
//       </span>
//     </div>
//   );
// };

// function TileHeader(props: React.PropsWithChildren) {
//   return <div className={'flex'}>{props.children}</div>;
// }

// function TileHeading(props: React.PropsWithChildren) {
//   return (
//     <Heading type={6}>
//       <span className={'font-normal text-gray-500 dark:text-gray-400'}>
//         {props.children}
//       </span>
//     </Heading>
//   );
// }

// function TileBody(props: React.PropsWithChildren) {
//   return <div className={'flex flex-col'}>{props.children}</div>;
// }

// function TileFigure(props: React.PropsWithChildren<{ figureClassName?: string }>) {
//   return <div className={`text-lg font-bold text-[#003FBC] ${props.figureClassName}`}>{props.children}</div>;
// }

// function TileTrend(
//   props: React.PropsWithChildren<{
//     trend: 'up' | 'down' | 'stale' | 'none';
//   }>,
// ) {
//   const Icon = useMemo(() => {
//     switch (props.trend) {
//       case 'up':
//         return <ArrowSmallUpIcon className={'h-4 text-green-500'} />;
//       case 'down':
//         return <ArrowSmallDownIcon className={'h-4 text-red-500'} />;
//       case 'stale':
//         return <Bars2Icon className={'h-4 text-yellow-500'} />;
//       case 'none':
//         return '';
//     }
//   }, [props.trend]);

//   return (
//     <TileBadge trend={props.trend}>
//       <span className={'flex items-center space-x-1 text-[#003FBC]'}>
//         {Icon}
//         <span>{props.children}</span>
//       </span>
//     </TileBadge>
//   );
// }

// function TileBadge(
//   props: React.PropsWithChildren<{
//     trend: 'up' | 'down' | 'stale' | 'none';
//   }>,
// ) {
//   const className = `inline-flex items-center rounded-lg py-1 px-2.5 text-xs font-semibold justify-center min-w-0`;

//   if (props.trend === `up`) {
//     return (
//       <div
//         className={`${className} bg-green-50 text-green-600 dark:bg-green-500/10`}
//       >
//         <span>{props.children}</span>
//       </div>
//     );
//   }

//   if (props.trend === `down`) {
//     return (
//       <div className={`${className} bg-red-50 text-red-600 dark:bg-red-500/10`}>
//         <span>{props.children}</span>
//       </div>
//     );
//   }

//   return (
//     <div
//       className={`${className} bg-yellow-50 text-yellow-600 dark:bg-yellow-500/10`}
//     >
//       <span>{props.children}</span>
//     </div>
//   );
// }

// Tile.Header = TileHeader;
// Tile.Heading = TileHeading;
// Tile.Body = TileBody;
// Tile.Figure = TileFigure;
// Tile.Trend = TileTrend;
// Tile.Badge = TileBadge;

// export default Tile;,

/**new design */

import { useMemo } from 'react';

import Heading from '~/core/ui/Heading';

import {
  ArrowSmallDownIcon,
  ArrowSmallUpIcon,
  Bars2Icon,
} from '@heroicons/react/24/outline';

const Tile: React.FC<{ children: React.ReactNode }> & {
  Header: typeof TileHeader;
  Heading: typeof TileHeading;
  Body: typeof TileBody;
  Figure: typeof TileFigure;
  Trend: typeof TileTrend;
  Badge: typeof TileBadge;
} = ({ children }: { children: React.ReactNode }) => {
  return (
    //added height h-24 for fix height
    <div
      className={
        'flex flex-col rounded-lg border border-gray-100 bg-background shadow-md ' +
        ' dark:border-dark-900 bg-background px-2 py-3 sm:px-3 sm:py-4'
      }
    >
      <span className='min-w-0'>
      {children}
      </span>
    </div>
  );
};

function TileHeader(props: React.PropsWithChildren) {
  return <div className={'flex'}>{props.children}</div>;
}

function TileHeading(props: React.PropsWithChildren) {
  return (
    <Heading type={6}>
      <span className={'font-normal text-gray-500 dark:text-gray-400 text-xs sm:text-sm break-words'}>
        {props.children}
      </span>
    </Heading>
  );
}

function TileBody(props: React.PropsWithChildren) {
  return <div className={'flex flex-col'}>{props.children}</div>;
}

function TileFigure(props: React.PropsWithChildren<{ figureClassName?: string }>) {
  return <div className={`text-lg sm:text-xl md:text-2xl lg:text-3xl font-bold text-primary text-right pt-4 break-words ${props.figureClassName}`}>{props.children}</div>;
}
// [#003FBC]
function TileTrend(
  props: React.PropsWithChildren<{
    trend: 'up' | 'down' | 'stale' | 'none';
  }>,
) {
  const Icon = useMemo(() => {
    switch (props.trend) {
      case 'up':
        return <ArrowSmallUpIcon className={'h-4 text-green-500'} />;
      case 'down':
        return <ArrowSmallDownIcon className={'h-4 text-red-500'} />;
      case 'stale':
        return <Bars2Icon className={'h-4 text-yellow-500'} />;
      case 'none':
        return '';
    }
  }, [props.trend]);

  return (
    <TileBadge trend={props.trend}>
      <span className={'flex items-center space-x-1 text-[#003FBC]'}>
        {Icon}
        <span>{props.children}</span>
      </span>
    </TileBadge>
  );
}

function TileBadge(
  props: React.PropsWithChildren<{
    trend: 'up' | 'down' | 'stale' | 'none';
  }>,
) {
  const className = `inline-flex items-center rounded-lg py-1 px-2.5 text-xs font-semibold justify-center min-w-0`;

  if (props.trend === `up`) {
    return (
      <div
        className={`${className} bg-green-50 text-green-600 dark:bg-green-500/10`}
      >
        <span>{props.children}</span>
      </div>
    );
  }

  if (props.trend === `down`) {
    return (
      <div className={`${className} bg-red-50 text-red-600 dark:bg-red-500/10`}>
        <span>{props.children}</span>
      </div>
    );
  }

  return (
    <div
      className={`${className} bg-yellow-50 text-yellow-600 dark:bg-yellow-500/10`}
    >
      <span>{props.children}</span>
    </div>
  );
}

Tile.Header = TileHeader;
Tile.Heading = TileHeading;
Tile.Body = TileBody;
Tile.Figure = TileFigure;
Tile.Trend = TileTrend;
Tile.Badge = TileBadge;

export default Tile;