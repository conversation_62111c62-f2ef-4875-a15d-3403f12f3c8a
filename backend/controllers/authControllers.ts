import User from "../models/user";
import boardingList from "../models/boardingList";
import APIFilters from "../utils/APIFilters";
import bcrypt from "bcryptjs";
import ErrorHandler from "../utils/errorHandler";
import configuration from "~/configuration";
import welcomeToBizOpsEmail from "~/lib/employees/welcomeEmail";
import ResponseModel from 'backend/utils/responseModel';
import LeavesQuota from "backend/models/leavesQuota";
import { backendMethodsText, changeEmployeeAccountStatusApiText, changeEmployementStatusApiText } from "~/common/constant";
import { backendText } from "backend/constant";
import { statusCodes } from "backend/common/enums";
import { registerNewEmployeeType } from 'backend/types/registerNewEmployeeType'
import { NextApiRequest, NextApiResponse } from "next";
import { sendEmail } from "backend/email/send-email";
import { employementStatusesText } from '~/common/constant';
import { getSession } from "next-auth/react";
import DefaultSettings from "../models/defaultSettings";
// import { getServerSession } from "next-auth";
// import { Options } from "~/app/api/auth/[...nextauth]/route";


// export const ChangePassword = async (req: NextApiRequest, res: NextApiResponse) => {
//   const { email, newPassword, currentPassword, newAccount } = req.body;

//   let user;

//   if(newAccount === true){
//   if (!email || !newPassword) {
//     return res.status(statusCodes.badRequest).json({ message: backendText.controller.authController.missingRequiredFields });
//   }
  
//   user = await User.findOne({ email });

//   if (!user) {
//     return res.status(404).json({ message: backendText.controller.authController.userNotFound });
//   }
// }

//   console.log("user", user)
//   if(newAccount === false){
//   if (!email || !newPassword || !currentPassword) {
//     return res.status(statusCodes.badRequest).json({ message: backendText.controller.authController.missingRequiredFields });
//   }

//   user = await User.findOne({ email }).select('+password');

//   if (!user) {
//     return res.status(404).json({ message: backendText.controller.authController.userNotFound });
//   }

//   const isCorrectPassword = await bcrypt.compare(
//     currentPassword,
//     user.password
//   );

//   if (!isCorrectPassword) {
//     return res.status(401).json({ message: backendText.controller.authController.incorrectCurrentPassword });
//   }
// }

//   const salt = await bcrypt.genSalt(10);
//   const hashPassword = await bcrypt.hash(newPassword, salt);

//   user.password = hashPassword;
//   // await user.save();
//   const updatedUser = await User.findByIdAndUpdate(
//     user._id,
//     { password: hashPassword },
//     { new: true, runValidators: true }
//   );
//   const response = new ResponseModel(true, backendText.controller.authController.passwordChangedSuccessfully, '');
//   res.status(statusCodes.success).json(response);
// };


export const ChangePassword = async (req: NextApiRequest, res: NextApiResponse) => {
  try {
    const { email, newPassword, currentPassword, newAccount } = req.body;

    // console.log("req", req.body)
    // console.log("newAccount", newAccount)
    if (!email || !newPassword || (!newAccount && !currentPassword)) {
      return res
        .status(statusCodes.badRequest)
        .json({ message: backendText.controller.authController.missingRequiredFields });
    }

    const user = newAccount
      ? await User.findOne({ email })
      : await User.findOne({ email }).select('+password');

    if (!user) {
      return res.status(404).json({ message: backendText.controller.authController.userNotFound });
    }

    if (!newAccount) {
      const isCorrectPassword = await bcrypt.compare(currentPassword, user.password);
      if (!isCorrectPassword) {
        return res
          .status(401)
          .json({ message: backendText.controller.authController.incorrectCurrentPassword });
      }
    }

    // Hash new password
    const salt = await bcrypt.genSalt(10);
    user.password = await bcrypt.hash(newPassword, salt);
     if(newAccount === true) {
      const currentDate = new Date();
      user.lastLoginIn = currentDate;
      }
    await user.save({ validateBeforeSave: false }); // Ensures pre-save hooks execute if defined

    res.status(statusCodes.success).json(
      new ResponseModel(true, backendText.controller.authController.passwordChangedSuccessfully, '')
    );
  } catch (error) {
    console.error('Error in ChangePassword:', error);
    res.status(statusCodes.internalServerError).json({ message: 'Internal Server Error' });
  }
};


export async function registerUser(req: NextApiRequest, res: NextApiResponse) {
  const { personalDetail, bankingDetail, officialDetail, employmentHistory, emergencyContact } = req.body;
  const name = personalDetail[0].firstName + ' ' + personalDetail[0].lastName;
  const email = officialDetail[0].companyEmail;
  const user = await User.create({
    name,
    email,
    personalDetail,
    bankingDetail,
    officialDetail,
    employmentHistory,
    emergencyContact
  });

  res.status(statusCodes.createdSuccessfully).json({
    user,
  });
};

export const updateEmployee = async (req: NextApiRequest, res: NextApiResponse, next: Function) => {
  let employee = await User.findById(req.query.id);

  if (!employee) {
    return next(new ErrorHandler(backendText.controller.authController.employeeNotFound, statusCodes.notFound));
  }

  const { personalDetail, bankingDetail, officialDetail, employmentHistory, emergencyContact } = req.body;
  const name = personalDetail[0].firstName + ' ' + personalDetail[0].lastName;
  const personalEmail = personalDetail[0].email;

  const user = await User.findByIdAndUpdate(req.query.id, {
    name,
    personalEmail,
    personalDetail,
    bankingDetail,
    officialDetail,
    employmentHistory,
    emergencyContact
  }, {
    new: true,
    upsert: true
  });

  res.status(statusCodes.success).json({
    user,
  });
};
/*account status change new*/
export const changeEmployeeAccountStatus = async (req: NextApiRequest, res: NextApiResponse, next: Function) => {
  try {
    const { id, isActive } = req.body;

    // Validate the input
    if (typeof isActive !== "string") {
      return res.status(statusCodes.badRequest).json({
        success: false,
        message: backendText.controller.authController.invalidValueIsActiveMustString,
      });
    }

    const userBoardingList = await boardingList.findById(id)



    // Update the isActive field only
    const updatedUser = await User.findByIdAndUpdate(
      id,
      { isActive },
      {
        new: true,
        runValidators: false,
      }

    );

    if (!updatedUser) {
      return res.status(statusCodes.notFound).json({
        success: false,
        message: backendText.controller.authController.employeeNotFound,
      });
    }

    res.status(statusCodes.success).json({
      success: true,
      message: changeEmployeeAccountStatusApiText.responseMessage,
      user: updatedUser,
    });
  } catch (error) {
    next(error);
  }
};

/*Employement status change */
export const changeEmployementStatus = async (req: NextApiRequest, res: NextApiResponse, next: Function) => {
  try {
    const { id, employementStatus } = req.body;

    // Validate the input
    if (typeof employementStatus !== "string") {
      return res.status(statusCodes.badRequest).json({
        success: false,
        message: backendText.controller.authController.invalidValueEmployementStatusMustString,
      });
    }

    // Update the isActive field only
    const updatedUser = await User.findByIdAndUpdate(
      id,
      { employementStatus },
      {
        new: true,
        runValidators: false,
      }

    );

    if (!updatedUser) {
      return res.status(statusCodes.notFound).json({
        success: false,
        message: backendText.controller.authController.employeeNotFound,
      });
    }

     console.log("leave quota function call")
     if (employementStatus.toLowerCase() === employementStatusesText.permanent.toLowerCase()) {
      console.log("userID", id)
      console.log("Adding leave quota for permanent employee")
     await addLeavesQuota(id, employementStatus, true);
   }


    res.status(statusCodes.success).json({
      success: true,
      message: changeEmployementStatusApiText.responseMessage,
      user: updatedUser,
    });

  } catch (error) {
    console.log("leaveQuotaRecorderror", error)

    next(error);
  }
};

/*getemployees with boardinglist */
// export async function getEmployees(req: NextApiRequest, res: NextApiResponse) {
//   // const { userId } = req.query;
//   // console.log("userId==>",userId)
//   const resPerPage = 1000;
//   const usersCount = await User.countDocuments();
 
//   /**Left to add missing fields in user model */
//   // await User.updateMany(
//   //   // { lastLoginIn: { $exists: false } }, // Select users where employementStatus is missing
//   //   // { $set: { password: "$2a$10$wNIVzQ59/w2KWsGMTN1KOOX7hb99v5ZCukWEeTnIKZ9AYbE9dZXy6" } }, // Set default value
//   //   {$set: {
//   //     "officialDetail.$[].shiftTimeStart": "09:00",  // New start time
//   //     "officialDetail.$[].shiftTimeEnd": "16:00"    // New end time
//   //   }}
//   // );

//   const apiFilters = new APIFilters(User.find(), req.query)
//     .search()
//     .filter();

//   let users = await apiFilters.query 
//   .collation({ locale: "en", numericOrdering: true }) // Enables numeric sorting for string-based numbers
//   .sort({ empID: -1 }); // Sorting by empID in descending order (largest first);

//   // Populate 'boardingList' data from 'boardingList' model
//   const usersWithBoardingStatus = await Promise.all(users.map(async (user: { _id: any; employementStatus: string; toObject: () => { }; }) => {

//     const boardingData = await boardingList.findOne({ userID: user._id });

//     // script left to manually add new user boardingList and update isActive status to active when  testing
//     if (!boardingData) {
//       await addOnBoardingList(user?._id)
//     }
//     await addLeavesQuota(user._id, user.employementStatus, false);
   
//     const isOnBoardingComplete = boardingData.isOnBoardingComplete || false;
//     const isOffBoardingComplete = boardingData.isOffBoardingComplete || false;
    
//     return {
//       ...user.toObject(),
//       isOnBoardingComplete,
//       isOffBoardingComplete,
//     };
//   }));

//   const filteredUsersCount = usersWithBoardingStatus.length;

//   apiFilters.pagination(resPerPage);

//   // Apply pagination to the populated users
//   users = await apiFilters.query.clone();

//   res.status(statusCodes.success).json({
//     usersCount,
//     resPerPage,
//     filteredUsersCount,
//     users: usersWithBoardingStatus,
//   });
// }

/**getEmployees new send all user */
// export async function getEmployees(req: NextApiRequest, res: NextApiResponse) {
//   const resPerPage = 1000;
  
//   // Execute count and filtered users query in parallel
//   const [usersCount, users] = await Promise.all([
//     User.countDocuments(),
//     new APIFilters(User.find().lean(), req.query) // Use .lean() for better performance
//       .search()
//       .filter()
//       .query
//       .collation({ locale: "en", numericOrdering: true })
//       .sort({ empID: -1 })
//   ]);

//   // Optimize boarding list lookup by doing a single query for all users
//   const userIds = users.map(user => user._id);
//   const boardingDataList = await boardingList.find(
//     { userID: { $in: userIds } },
//     { userID: 1, isOnBoardingComplete: 1, isOffBoardingComplete: 1 }
//   ).lean();

//   // Create a map for O(1) lookups
//   const boardingDataMap = boardingDataList.reduce<Record<string, any>>((acc, data) => {
//     acc[data.userID.toString()] = data;
//     return acc;
//   }, {});

//   // Merge user and boarding data
//   const usersWithBoardingStatus = users.map(user => {
//     const boardingData = boardingDataMap[user._id.toString()] || {};
//     return {
//       ...user,
//       isOnBoardingComplete: boardingData.isOnBoardingComplete || false,
//       isOffBoardingComplete: boardingData.isOffBoardingComplete || false
//     };
//   });

//   const filteredUsersCount = usersWithBoardingStatus.length;

//   // Apply pagination
//   const page = parseInt(req.query.page as string) || 1;
//   const startIndex = (page - 1) * resPerPage;
//   const paginatedUsers = usersWithBoardingStatus.slice(startIndex, startIndex + resPerPage);

//   res.status(statusCodes.success).json({
//     usersCount,
//     resPerPage,
//     filteredUsersCount,
//     users: paginatedUsers,
//   });
// }

/**Get employees latest for client side page */
// export async function getEmployees(req: NextApiRequest, res: NextApiResponse) {
//   const resPerPage = 10;
//   console.log("Incoming Query Params:", req.query);
//   // Execute count and filtered users query in parallel
//   const [usersCount, users] = await Promise.all([
//     User.countDocuments(),
//     new APIFilters(User.find().lean(), req.query) // Use .lean() for better performance
//       .search()
//       .filter()
//       .query
//       .collation({ locale: "en", numericOrdering: true })
//       .sort({ empID: -1 })
//   ]);
  
//   // Optimize boarding list lookup by doing a single query for all users
//   const userIds = users.map(user => user._id);
//   const boardingDataList = await boardingList.find(
//     { userID: { $in: userIds } },
//     { userID: 1, isOnBoardingComplete: 1, isOffBoardingComplete: 1 }
//   ).lean();

//   // Create a map for O(1) lookups
//   const boardingDataMap = boardingDataList.reduce<Record<string, any>>((acc, data) => {
//     acc[data.userID.toString()] = data;
//     return acc;
//   }, {});

//   // Merge user and boarding data
//   const usersWithBoardingStatus = users.map(user => {
//     const boardingData = boardingDataMap[user._id.toString()] || {};
//     return {
//       ...user,
//       isOnBoardingComplete: boardingData.isOnBoardingComplete || false,
//       isOffBoardingComplete: boardingData.isOffBoardingComplete || false
//     };
//   });

//   const filteredUsersCount = usersWithBoardingStatus.length;

//   // Apply pagination
//   const page = parseInt(req.query.page as string) || 1;
//   const startIndex = (page - 1) * resPerPage;
//   const paginatedUsers = usersWithBoardingStatus.slice(startIndex, startIndex + resPerPage);
//   const totalPages = Math.ceil(filteredUsersCount / resPerPage);

//   res.status(statusCodes.success).json({
//     usersCount,
//     resPerPage,
//     filteredUsersCount,
//     totalPages,
//     users: paginatedUsers,
//   });
// }

/**Get employee with new filteration */
export async function getEmployees(req: NextApiRequest, res: NextApiResponse) {
  const resPerPage = 10;
  console.log("Incoming Query Params:", req.query);

  const { activeStatus, employementStatus, employeeId, page } = req.query;

  // Dynamically build MongoDB filter object
  const filter: Record<string, any> = {};

  if ((activeStatus !== "") && activeStatus !== undefined) {
    filter.isActive = activeStatus;
  }

  if (employementStatus !== "" && employementStatus !== undefined) {
    filter.employementStatus = employementStatus;
  }

  if (employeeId !== "" && employeeId !== undefined) {
    filter._id = employeeId;
  }

  const currentPage = parseInt(page as string) || 1;
  const skip = (currentPage - 1) * resPerPage;

  // Get total count and paginated users
  const [usersCount, filteredUsersCount, users] = await Promise.all([
    User.countDocuments(), // total count (no filters)
    User.countDocuments(filter), // filtered count
    User.find(filter)
      .lean()
      .collation({ locale: "en", numericOrdering: true })
      .sort({ empID: -1 })
      .skip(skip)
      .limit(resPerPage),
  ]);

  const userIds = users.map(user => user._id);
  const boardingDataList = await boardingList.find(
    { userID: { $in: userIds } },
    { userID: 1, isOnBoardingComplete: 1, isOffBoardingComplete: 1 }
  ).lean();

  const boardingDataMap = boardingDataList.reduce<Record<string, any>>((acc, data) => {
    acc[data.userID.toString()] = data;
    return acc;
  }, {});

  const usersWithBoardingStatus = users.map((user:any) => {
    const boardingData = boardingDataMap[user._id.toString()] || {};
    return {
      ...user,
      isOnBoardingComplete: boardingData.isOnBoardingComplete || false,
      isOffBoardingComplete: boardingData.isOffBoardingComplete || false
    };
  });

  const totalPages = Math.ceil(filteredUsersCount / resPerPage);

  res.status(200).json({
    usersCount,
    resPerPage,
    filteredUsersCount,
    totalPages,
    users: usersWithBoardingStatus,
  });
}



export const getUser = async (req: NextApiRequest, res: NextApiResponse) => {
  const user = await User.findById(req.query.id);

  if (!user) {
    res.status(statusCodes.notFound).json({
      error: backendText.controller.authController.notFoundUser,
    });
  }

  res.status(statusCodes.success).json({
    user,
  });
};

/*Get active Employee*/
export const getEmployee = async (req: NextApiRequest, res: NextApiResponse) => {
  const resPerPage = 100;

  // Count total number of users
  const usersCount = await User.countDocuments();

  // Initialize API filters with search and custom filters based on query parameters
  const apiFilters = new APIFilters(User.find(), req.query)
    .search()
    .filter();

  // Add the isActive filter for only active users
  apiFilters.query = apiFilters.query.where("isActive").ne("inactive"); // Adding the condition for isActive

  // Fetch employees based on the applied filters
  let employees = await apiFilters.query;

  // Get the count of filtered users
  const filteredUsersCount = employees.length;

  // Apply pagination
  apiFilters.pagination(resPerPage);

  // Fetch employees with pagination applied
  employees = await apiFilters.query.clone();

  // Send the response with counts and the employee data
  res.status(statusCodes.success).json({
    usersCount,
    resPerPage,
    filteredUsersCount,
    employees,
  });
};

/**Get active employees */
// export const getEmployee = async (req: NextApiRequest, res: NextApiResponse) => {
//   if (req.method !== "GET") {
//     return res.status(405).json({ message: "Method Not Allowed" });
//   }

//   try {
//     const resPerPage = 100;
//     const usersCount = await User.countDocuments();

//     const apiFilters = new APIFilters(User.find(), req.query)
//       .search()
//       .filter();

//     apiFilters.query = apiFilters.query.where("isActive").ne("inactive");
//     let employees = await apiFilters.query;
//     const filteredUsersCount = employees.length;
    
//     apiFilters.pagination(resPerPage);
//     employees = await apiFilters.query.clone();

//     res.status(200).json({
//       success: true,
//       usersCount,
//       resPerPage,
//       filteredUsersCount,
//       employees,
//     });
//   } catch (error) {
//     console.error("Error fetching employees:", error);
//     res.status(500).json({ success: false, message: "Internal Server Error" });
//   }
// }

async function registerEmployeeEmail(email = '', password = '') {

  const cpanelUsername = 'devpanel';
  const cpanelToken = 'I5LAEN41LZNPH74LXW4QBGKWY4TE1C2V';
  const quota = '250';

  // cPanel API URL
  const apiUrl = `https://devexcelit.com:2083/json-api/cpanel?cpanel_jsonapi_user=${cpanelUsername}&cpanel_jsonapi_apiversion=2&cpanel_jsonapi_module=Email&cpanel_jsonapi_func=addpop`;

  // Data to be sent in the request
  const data = {
    email: email,
    password: password,
    quota: quota,
  };

  try {
    // Make API request
    const response = await fetch(apiUrl, {
      method: backendMethodsText.post,
      headers: {
        'Authorization': `cpanel ${cpanelUsername}:${cpanelToken}`,
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams(data),
    });

    // Handle response
    if (response.ok) {
      const responseData = await response.json();
      if (responseData.cpanelresult.data[0].result === 1) {
      } else {
      }
    } else {
    }
  } catch (error) {
  }

}

function generateRandomPassword(length: number) {
  const lowercaseChars = backendText.controller.authController.lowercaseChars;
  const uppercaseChars = backendText.controller.authController.uppercaseChars;
  const numberChars = backendText.controller.authController.numberChars;
  const specialChars = backendText.controller.authController.specialChars;

  const allChars = lowercaseChars + uppercaseChars + numberChars + specialChars;

  let password = '';
  // Ensure the password includes at least one character from each character set
  password += lowercaseChars.charAt(Math.floor(Math.random() * lowercaseChars.length));
  password += uppercaseChars.charAt(Math.floor(Math.random() * uppercaseChars.length));
  password += numberChars.charAt(Math.floor(Math.random() * numberChars.length));
  password += specialChars.charAt(Math.floor(Math.random() * specialChars.length));

  // Generate the remaining characters randomly from all character sets
  for (let i = 4; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * allChars.length);
    password += allChars.charAt(randomIndex);
  }

  // Shuffle the password to ensure the first four characters are not in the same order
  password = password.split('').sort(() => 0.5 - Math.random()).join('');

  return password;
}

async function addLeavesQuota(userID: string, employmentStatus: string, isEmploymentStatusChanged: boolean) {
  let totalLeave = 0;
  let annualLeave = 0;
  let sickLeave = 0;
  let causualLeave = 0;
  const currentDate = new Date();
  const currentMonth = currentDate.getMonth() + 1;
  const year = currentDate.getFullYear();

  const companyAllocated = {
    totalLeave: 0,
    annualLeave: 0,
    sickLeave: 0,
    causualLeave: 0,
  };

  if (employmentStatus.toLowerCase() === employementStatusesText.internship.toLowerCase() || employmentStatus.toLowerCase() === employementStatusesText.probation.toLowerCase()) {
    // For internship or probation status
    totalLeave = 3;
    annualLeave = 0;
    causualLeave = 3;
  } else if (employmentStatus.toLowerCase() === employementStatusesText.permanent.toLowerCase()) {
    // For permanent employees
    companyAllocated.totalLeave = 24;
    companyAllocated.annualLeave = 12;
    companyAllocated.sickLeave = 6;
    companyAllocated.causualLeave = 6;

    const remainingMonths = (12 - currentMonth) + 1;
    const perMonthLeave = companyAllocated.totalLeave / 12;
    companyAllocated.totalLeave = remainingMonths * perMonthLeave;

    // Calculate leave proportions
    totalLeave = Math.round(companyAllocated.totalLeave);
    annualLeave = Math.round(totalLeave / 2);
    causualLeave = annualLeave * 0.5;
    sickLeave = annualLeave * 0.5;
  }

  // Remaining leave balances
  const remainingAnnualLeave = annualLeave;
  const remainaingCasualLeave = causualLeave;
  const remainingSickLeave = sickLeave;
  let casualLeaveSetup=0;

  const carryForward = false;
  if (isEmploymentStatusChanged) {
    const mongoose = require('mongoose');
    const objectId = new mongoose.Types.ObjectId(userID);
    // console.log("objectId", objectId)

    const existingLeaveQuota = await LeavesQuota.findOne({ userID: objectId });
    if (existingLeaveQuota) {
      // Update other leave balances as necessary (if required)
      if(existingLeaveQuota.remainaingCasualLeave > 0){
        casualLeaveSetup = existingLeaveQuota.remainaingCasualLeave ;
      }else{
        casualLeaveSetup =0;
      }
      existingLeaveQuota.carryForward =  casualLeaveSetup;
      existingLeaveQuota.totalLeave = totalLeave +  casualLeaveSetup;
      existingLeaveQuota.annualLeave = annualLeave;
      existingLeaveQuota.sickLeave = sickLeave;
      existingLeaveQuota.causualLeave = remainaingCasualLeave + casualLeaveSetup;
      existingLeaveQuota.remainingAnnualLeave = remainingAnnualLeave;
      existingLeaveQuota.remainaingCasualLeave = existingLeaveQuota.causualLeave;
      existingLeaveQuota.remainingSickLeave = remainingSickLeave;
      // Save the updated leave record
      await existingLeaveQuota.save();
    } else {
      // console.log("existingLeaveQuota", false)

    }
  } else { // Create the user's leave record
    const user = await LeavesQuota.create({
      year,
      totalLeave,
      annualLeave,
      causualLeave,
      sickLeave,
      remainingAnnualLeave,
      remainaingCasualLeave,
      remainingSickLeave,
      carryForward,
      userID,
    });
  }

}


function roundIfNumberISGreaterThanFive(num: number) {
  const integerPart = Math.floor(num);
  const decimalPart = num - integerPart;

  if (decimalPart > 0.5) {
    return Math.ceil(num);
  } else if (decimalPart === 0.5) {
    return num;
  } else {
    return integerPart + 0.5;
  }
}


async function sendWelcomeEmailEmployee(to: string, name: string, email: string, password: string) {
  // console.log("API Hit!");
  const productName = configuration.site.siteName;
  const accountCreationEmail = welcomeToBizOpsEmail({
    productName,
    userDisplayName: name,
    email: email,
    password: password
  });

  const subject = `Welcome to ${productName} By DevExcel`;
  const from = process.env.EMAIL_USER;

  if (!from) {
    throw new Error(backendText.controller.authController.missingEMAIL_SENDEREnvVariable);
  }

  return sendEmail({
    to: to,
    subject,
    html: accountCreationEmail,
    from,
  });
}

async function addOnBoardingList(userID: string) {

  let onbBoardingList = [
    {
      title: backendText.controller.authController.contactPreviousEmployerForFeedback,
      isComplete: false
    },
    {
      title: backendText.controller.authController.offerLetterWithSignature,
      isComplete: false
    },
    {
      title: backendText.controller.authController.appointmentLetterEmployeePolicyHandbook,
      isComplete: false
    },
    {
      title: backendText.controller.authController.signatureAgreementPolicies,
      isComplete: false
    },
    {
      title: backendText.controller.authController.collectAllOfficialDocumentsIncludingCnicCopyEducationalDegrees,
      isComplete: false
    },
    {
      title: backendText.controller.authController.bankAccountOpeningProcess,
      isComplete: false
    },
    {
      title: backendText.controller.authController.sendOfficialCredential,
      isComplete: false
    },
    {
      title: backendText.controller.authController.createOfficialGmailAccount,
      isComplete: false
    },
    {
      title: backendText.controller.authController.prepareSystemNewEmployee,
      isComplete: false
    },
    {
      title: backendText.controller.authController.setupSlackEmployeesLaptopHandingGoodieBag,
      isComplete: false
    },
    {
      title: backendText.controller.authController.orientation,
      isComplete: false
    },
    {
      title: backendText.controller.authController.introductionWithTeam,
      isComplete: false
    },
    {
      title: backendText.controller.authController.photoSlackLinkedinPost,
      isComplete: false
    },
    {
      title: backendText.controller.authController.biometricRegisterationMachine,
      isComplete: false
    },
    {
      title: backendText.controller.authController.instructCandidateAddjoiningDevExcelLinkedinCoverEmployementDetails,
      isComplete: false
    },
    {
      title: backendText.controller.authController.sendEmailSlackMessageCongratulateEmployee,
      isComplete: false
    },
    {
      title: backendText.controller.authController.addEmployeeOfficialGroupsSlackChannels,
      isComplete: false
    },
    {
      title: backendText.controller.authController.sendEmailAzeemAddPermanentEmployee,
      isComplete: false
    }
  ]

  let offBoardingList = [
    {
      title: backendText.controller.authController.collectSystemEmployeeAfterWipingData,
      isComplete: false
    },
    {
      title: backendText.controller.authController.redMarkEmployeeUpdatedSheet,
      isComplete: false
    },
    {
      title: backendText.controller.authController.sendemailAzeemDeleteRecordPermanent,
      isComplete: false
    },
    {
      title: backendText.controller.authController.companyFarewell,
      isComplete: false
    },
    {
      title: backendText.controller.authController.provideExperienceLetter,
      isComplete: false
    },
    {
      title: backendText.controller.authController.informSirJawadSarwar,
      isComplete: false
    },
    {
      title: backendText.controller.authController.updateStatusSlack,
      isComplete: false
    },
    {
      title: backendText.controller.authController.affidavitSignature,
      isComplete: false
    },
    {
      title: backendText.controller.authController.updateEmployeeStatusBizOps,
      isComplete: false
    },
    {
      title: backendText.controller.authController.disableEmailAccount,
      isComplete: false
    },
    {
      title: backendText.controller.authController.removeEmployeeFromWhatsappGroup,
      isComplete: false
    }
  ]

  await boardingList.create({
    userID,
    onBoardingList: onbBoardingList,
    offBoardingList,
  })

}

/*Register new employee with automatically generating empID on the backend */
export const registerNewEmployee = async (req: NextApiRequest, res: NextApiResponse) => {
  try {
    const { fatherName, firstName, lastName, personalEmail, companyEmail, phone, employementStatus, password } = req.body;
 
    const name = `${firstName} ${lastName}`;
    const email = companyEmail.toLowerCase();
    const personalMail = personalEmail.toLowerCase()
 
    //Check if user already exists before processing further
        const existingUser = await User.findOne({
            $or: [{ personalEmail:personalMail }, { email: companyEmail }, { 'personalDetail.phone': phone }]
          }).lean()  as unknown as { personalEmail?: string; email?: string; personalDetail?: { phone: string | undefined }[] } | null; // Use .lean() to improve performance
 
    if (existingUser) {
            const existingFields = [];
            if (existingUser.personalEmail === personalEmail) {
              existingFields.push(backendText.controller.authController.personalEmail);
            }
            if (existingUser.email === companyEmail) {
              existingFields.push(backendText.controller.authController.companyEmail);
            }
            if (existingUser.personalDetail && existingUser.personalDetail.some((detail: { phone: string | undefined; }) => detail.phone === phone)) {
              existingFields.push(backendText.controller.authController.phoneNumber);
            }
            return res.status(statusCodes.badRequest).json({
              success: false,
              message: `${existingFields.join(', ')} already exist.`,
            });
          }
 
    // Send welcome email before database entry
    const subject = `Welcome to Bizops By DevExcel`;
    const from = process.env.EMAIL_USER;
    const productName = configuration.site.siteName;
    // console.log("Sending email...");
 
    await sendEmail({
      from,
      to: personalMail,
      subject,
      html: welcomeToBizOpsEmail({
        productName,
        userDisplayName: name,
        email: email,
        password: password
      }),
    });
 
    // console.log("Email sent successfully.");
 
    // Generate new Employee ID
    const lastEmployee = await User.aggregate([
      { $addFields: { empIDInt: { $toInt: "$empID" } } },
      { $sort: { empIDInt: -1 } },
      { $limit: 1 },
      { $project: { empID: 1 } }
    ]);
 
    const lastEmpID = lastEmployee.length > 0 ? parseInt(lastEmployee[0].empID, 10) : 0;
    const newEmpID = (lastEmpID + 1).toString().padStart(2, '0');
 
    // Hash password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);

    // Get default settings
    const defaultSettings = await DefaultSettings.findOne({});
    
    // Create user with basic official details
    const user = await User.create({
      empID: newEmpID,
      name,
      email,
      personalEmail: personalMail,
      personalDetail: [{ email: personalMail, fatherName, firstName, lastName, phone }],
      officialDetail: [{ companyEmail }],
      password: hashedPassword,
      employementStatus
    });
    
    // Apply default settings if available
    if (defaultSettings && user.officialDetail && user.officialDetail.length > 0) {
      user.officialDetail[0].shiftTimeStart = defaultSettings.shiftTimeStart;
      user.officialDetail[0].shiftTimeEnd = defaultSettings.shiftTimeEnd;
      user.officialDetail[0].bufferTime = defaultSettings.bufferTime;
      user.officialDetail[0].paidLateCounts = defaultSettings.paidLateCounts;
      await user.save();
    }
    
    // console.log("User added to database successfully.");
 
    // Run background tasks (not waiting for these)
    Promise.all([
      addOnBoardingList(user._id),
      addLeavesQuota(user._id, user.employementStatus, false)
    ]).catch(console.error);
 
    // Return success response
    res.status(200).json({ success: true, message: "Employee created successfully", user });
 
  } catch (error) {
    console.log("Error during registration:", error);
    res.status(500).json({ success: false, message: "Server error", error });
  }
};


/**newww */
// export const registerNewEmployee = async (req: NextApiRequest, res: NextApiResponse) => {
//   try {
//     const { fatherName, firstName, lastName, personalEmail, companyEmail, phone, sendEmail, employementStatus, password } = req.body;

//     const name = `${firstName} ${lastName}`;
//     const email = companyEmail;

//     // Parallel execution
//     const [salt, lastEmployee] = await Promise.all([
//       bcrypt.genSalt(10),
//       User.aggregate([
//         { $addFields: { empIDInt: { $toInt: "$empID" } } },
//         { $sort: { empIDInt: -1 } },
//         { $limit: 1 },
//         { $project: { empID: 1 } }
//       ])
//     ]);

//     const hashedPassword = await bcrypt.hash(password, salt);

//     // Fast indexed search
//     const existingUser = await User.findOne({
//       $or: [{ personalEmail }, { email: companyEmail }, { 'personalDetail.phone': phone }]
//     }).lean()  as unknown as { personalEmail?: string; email?: string; personalDetail?: { phone: string | undefined }[] } | null; // Use .lean() to improve performance

//         if (existingUser) {
//       const existingFields = [];
//       if (existingUser.personalEmail === personalEmail) {
//         existingFields.push(backendText.controller.authController.personalEmail);
//       }
//       if (existingUser.email === companyEmail) {
//         existingFields.push(backendText.controller.authController.companyEmail);
//       }
//       if (existingUser.personalDetail && existingUser.personalDetail.some((detail: { phone: string | undefined; }) => detail.phone === phone)) {
//         existingFields.push(backendText.controller.authController.phoneNumber);
//       }
//       return res.status(statusCodes.badRequest).json({
//         success: false,
//         message: `${existingFields.join(', ')} already exist.`,
//       });
//     }

//     // Generate new Employee ID
//     const lastEmpID = lastEmployee.length > 0 ? parseInt(lastEmployee[0].empID, 10) : 0;
//     const newEmpID = (lastEmpID + 1).toString().padStart(2, '0');

//     // Insert user into DB
//     const user = await User.create({
//       empID: newEmpID,
//       name,
//       email,
//       personalEmail,
//       personalDetail: [{ email: personalEmail, fatherName, firstName, lastName, phone }],
//       officialDetail: [{ companyEmail }],
//       password: hashedPassword,
//       employementStatus
//     });

//     if (user) {
//       // Run background tasks
//       Promise.all([
//         addOnBoardingList(user._id),
//         addLeavesQuota(user._id, user.employementStatus, false)
//       ]).catch(console.error);
//     }

//     // Return success response
//     res.status(200).json({ success: true, message: "Employee created successfully", user });

//   } catch (error) {
//     res.status(500).json({ success: false, message: "Server error", error });
//   }
// };

export const updateShiftTime = async (req: NextApiRequest, res: NextApiResponse) => {
  try {
    const { shiftTimeStart, shiftTimeEnd, bufferTime, paidLateCounts } = req.body;

    // Validate required fields
    if (!shiftTimeStart || !shiftTimeEnd || bufferTime === undefined || paidLateCounts === undefined) {
      return res.status(statusCodes.badRequest).json({
        success: false,
        message: "Missing required fields: shiftTimeStart, shiftTimeEnd, bufferTime, and paidLateCounts are required",
      });
    }

    // Update or create default settings
    const updatedDefaultSettings = await DefaultSettings.findOneAndUpdate(
      {}, // empty filter to match any document
      {
        shiftTimeStart,
        shiftTimeEnd,
        bufferTime,
        paidLateCounts,
      },
      { upsert: true, new: true }
    );

    // Update all users' official details
    const result = await User.updateMany(
      {}, // match all users
      {
        $set: {
          "officialDetail.$[].shiftTimeStart": shiftTimeStart,
          "officialDetail.$[].shiftTimeEnd": shiftTimeEnd,
          "officialDetail.$[].bufferTime": bufferTime,
          "officialDetail.$[].paidLateCounts": paidLateCounts,
        }
      }
    );

    return res.status(statusCodes.success).json({
      success: true,
      message: "Shift time settings updated successfully",
      defaultSettings: updatedDefaultSettings,
      usersUpdated: result.modifiedCount,
    });
  } catch (error: any) {
    console.error("Error updating shift time settings:", error);
    return res.status(statusCodes.internalServerError).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
};

/**
 * Get default shift time settings
 * Retrieves the default shift time settings from the defaultSettings collection
 */
export const getDefaultShiftTime = async (req: NextApiRequest, res: NextApiResponse) => {
  try {
    // Find the default settings document
    const defaultSettings = await DefaultSettings.findOne({});
    
    if (!defaultSettings) {
      return res.status(statusCodes.notFound).json({
        success: false,
        message: "Default shift time settings not found",
      });
    }
    
    return res.status(statusCodes.success).json({
      success: true,
      message: "Default shift time settings retrieved successfully",
      defaultSettings,
    });
  } catch (error: any) {
    console.error("Error retrieving default shift time settings:", error);
    return res.status(statusCodes.internalServerError).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
};


